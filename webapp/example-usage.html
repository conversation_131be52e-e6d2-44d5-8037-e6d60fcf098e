<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态水印使用示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .example-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007cba;
            color: white;
        }
        button:hover {
            background-color: #005a87;
        }
        .info {
            background-color: #f0f8ff;
            padding: 10px;
            border-left: 4px solid #007cba;
            margin: 10px 0;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>动态水印使用示例</h1>
        
        <div class="info">
            <strong>功能说明：</strong>
            <ul>
                <li>自动调用后端API获取当前用户信息（只调用一次）</li>
                <li>动态构建水印文本：用户名换行时间</li>
                <li>每5秒自动更新时间显示</li>
                <li>支持插件有效性检查（无效时不显示水印）</li>
                <li>API调用失败时不显示水印</li>
                <li>支持换行显示和自定义格式</li>
            </ul>
        </div>

        <div class="example-section">
            <h3>一步到位 - 快速启动动态水印</h3>
            <div class="code">// 最简单的方式 - 使用默认配置
watermark.start();

// 或者自定义配置
watermark.start({
    watermark_alpha: 0.15,
    watermark_fontsize: '16px',
    watermark_color: '#666666'
});</div>
            <button onclick="quickStart()">快速启动动态水印</button>
        </div>

        <div class="example-section">
            <h3>传统方式（兼容旧版本）</h3>
            <div class="code">watermark.init({
    dynamic_update: true,
    watermark_alpha: 0.15,
    watermark_fontsize: '16px',
    watermark_color: '#666666'
});</div>
            <button onclick="initBasicWatermark()">启动基本动态水印</button>
        </div>

        <div class="example-section">
            <h3>自定义配置</h3>
            <div class="code">watermark.init({
    dynamic_update: true,
    api_url: '/polarion/watermark/api/currentUser',
    fallback_text: '系统水印',
    time_format: 'YYYY-MM-DD HH:mm:ss',
    watermark_alpha: 0.2,
    watermark_fontsize: '14px',
    watermark_color: '#333333',
    watermark_width: 200,
    watermark_angle: 20
});</div>
            <button onclick="initCustomWatermark()">启动自定义动态水印</button>
        </div>

        <div class="example-section">
            <h3>快速启动静态水印</h3>
            <div class="code">// 最简单的方式
watermark.startStatic('我的静态水印');

// 或者自定义配置
watermark.startStatic('静态水印文本', {
    watermark_alpha: 0.1,
    watermark_fontsize: '14px'
});</div>
            <button onclick="quickStartStatic()">快速启动静态水印</button>
        </div>

        <div class="example-section">
            <h3>传统静态水印方式</h3>
            <div class="code">watermark.init({
    dynamic_update: false,
    watermark_txt: '静态水印文本',
    watermark_alpha: 0.1
});</div>
            <button onclick="initStaticWatermark()">启动静态水印</button>
        </div>

        <div class="example-section">
            <h3>控制功能</h3>
            <button onclick="refreshUserData()">刷新用户数据</button>
            <button onclick="showCurrentUserData()">显示当前用户数据</button>
            <button onclick="removeWatermark()">移除水印</button>
        </div>

        <div class="example-section">
            <h3>当前状态</h3>
            <div id="status" class="info">等待操作...</div>
        </div>
    </div>

    <script src="javascript/watermark.js"></script>
    <script>
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }

        function quickStart() {
            watermark.start({
                watermark_alpha: 0.15,
                watermark_fontsize: '16px',
                watermark_color: '#666666'
            });
            updateStatus('快速动态水印已启动');
        }

        function quickStartStatic() {
            watermark.startStatic('我的静态水印', {
                watermark_alpha: 0.1,
                watermark_fontsize: '14px'
            });
            updateStatus('快速静态水印已启动');
        }

        function initBasicWatermark() {
            watermark.init({
                dynamic_update: true,
                watermark_alpha: 0.15,
                watermark_fontsize: '16px',
                watermark_color: '#666666'
            });
            updateStatus('基本动态水印已启动');
        }

        function initCustomWatermark() {
            watermark.init({
                dynamic_update: true,
                api_url: '/polarion/watermark/api/currentUser',
                fallback_text: '系统水印',
                time_format: 'YYYY-MM-DD HH:mm:ss',
                watermark_alpha: 0.2,
                watermark_fontsize: '14px',
                watermark_color: '#333333',
                watermark_width: 200,
                watermark_angle: 20
            });
            updateStatus('自定义动态水印已启动');
        }

        function initStaticWatermark() {
            watermark.init({
                dynamic_update: false,
                watermark_txt: '静态水印文本',
                watermark_alpha: 0.1
            });
            updateStatus('静态水印已启动');
        }

        function refreshUserData() {
            watermark.refreshUserData();
            updateStatus('用户数据刷新请求已发送');
        }

        function showCurrentUserData() {
            var userData = watermark.getCurrentUserData();
            if (userData) {
                updateStatus('当前用户数据：<br>' + JSON.stringify(userData, null, 2));
            } else {
                updateStatus('暂无用户数据');
            }
        }

        function removeWatermark() {
            watermark.remove();
            updateStatus('水印已移除');
        }

        // 页面加载完成后的默认行为
        window.addEventListener('load', function() {
            updateStatus('页面已加载，请选择水印类型进行测试');
        });
    </script>
</body>
</html>
