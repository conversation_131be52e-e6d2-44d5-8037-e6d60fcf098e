package com.fasnote.alm.watermark;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.polarion.platform.core.PlatformContext;
import com.polarion.platform.security.ISecurityService;

public class CurrentUserServlet extends HttpServlet{

	@Override
	protected void service(HttpServletRequest arg0, HttpServletResponse arg1) throws ServletException, IOException {
		ISecurityService securityService = PlatformContext.getPlatform().lookupService(ISecurityService.class);
		String currentUser = securityService.getCurrentUser();
		
	}
	
}
