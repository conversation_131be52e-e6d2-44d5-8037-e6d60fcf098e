package com.fasnote.alm.watermark;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.polarion.platform.core.PlatformContext;
import com.polarion.platform.security.ISecurityService;

public class CurrentUserServlet extends HttpServlet {

	private static final long serialVersionUID = 1L;
	private final Gson gson = new Gson();

	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		// 设置响应头
		response.setContentType("application/json");
		response.setCharacterEncoding("UTF-8");
		response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
		response.setHeader("Pragma", "no-cache");
		response.setDateHeader("Expires", 0);

		PrintWriter out = response.getWriter();

		try {
			// 获取当前用户信息
			ISecurityService securityService = PlatformContext.getPlatform().lookupService(ISecurityService.class);
			String currentUser = securityService.getCurrentUser();

			// 构建响应数据
			Map<String, Object> responseData = new HashMap<>();

			if (currentUser != null && !currentUser.trim().isEmpty()) {
				responseData.put("success", true);
				responseData.put("currentUser", currentUser);
				responseData.put("pluginActive", true); // 插件有效性判断，后续可扩展
				responseData.put("timestamp", System.currentTimeMillis());
				response.setStatus(HttpServletResponse.SC_OK);
			} else {
				responseData.put("success", false);
				responseData.put("currentUser", null);
				responseData.put("pluginActive", true);
				responseData.put("error", "用户未登录或无法获取用户信息");
				responseData.put("timestamp", System.currentTimeMillis());
				response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
			}

			// 输出 JSON 响应
			String jsonResponse = gson.toJson(responseData);
			out.print(jsonResponse);

		} catch (Exception e) {
			// 异常处理
			Map<String, Object> errorResponse = new HashMap<>();
			errorResponse.put("success", false);
			errorResponse.put("currentUser", null);
			errorResponse.put("pluginActive", false);
			errorResponse.put("error", "服务器内部错误: " + e.getMessage());
			errorResponse.put("timestamp", System.currentTimeMillis());

			response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			String jsonResponse = gson.toJson(errorResponse);
			out.print(jsonResponse);
		} finally {
			out.flush();
			out.close();
		}
	}

	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		// POST 请求重定向到 GET 处理
		doGet(request, response);
	}
}
