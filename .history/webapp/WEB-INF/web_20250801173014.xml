<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	version="3.0">

	
	<filter>
		<filter-name>DoAsFilter</filter-name>
		<filter-class>com.polarion.portal.tomcat.servlets.DoAsFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>DoAsFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- CurrentUserServlet 配置 -->
	<servlet>
		<servlet-name>CurrentUserServlet</servlet-name>
		<servlet-class>com.fasnote.alm.watermark.CurrentUserServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>CurrentUserServlet</servlet-name>
		<url-pattern>/api/currentUser</url-pattern>
	</servlet-mapping>

	<security-constraint>
		<web-resource-collection>
			<web-resource-name>All</web-resource-name>
			<url-pattern>/*</url-pattern>
		</web-resource-collection>
		<auth-constraint>
			<role-name>user</role-name>
		</auth-constraint>
	</security-constraint>
	<login-config>
		<auth-method>BASIC</auth-method>
		<realm-name>PolarionRealm</realm-name>
	</login-config>
</web-app>
