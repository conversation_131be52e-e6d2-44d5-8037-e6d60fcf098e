(function (root,factory) {
  if (typeof define === 'function' && define.amd) {
    /*AMD. Register as an anonymous module.
    *define([], factory); */
    define([], factory());
  } else if (typeof module === 'object' && module.exports) {
    /*Node. Does not work with strict CommonJS, but
    // only CommonJS-like environments that support module.exports,
    // like Node.*/
    module.exports = factory();

  } else {
    /*Browser globals (root is window)*/
    root['watermark'] = factory();
  }
}(this, function () {

  /*Just return a value to define the module export.*/
  var watermark = {};

  var forceRemove = false;

  // 动态水印相关变量
  var currentUserData = null;
  var updateTimer = null;
  var apiCallInProgress = false;
  var apiCalled = false; // 标记API是否已调用过
  var TIME_UPDATE_INTERVAL = 5 * 1000; // 5秒更新一次时间

  var defaultSettings={
    watermark_id: 'wm_div_id',          //水印总体的id
    watermark_prefix: 'mask_div_id',    //小水印的id前缀
    watermark_txt:"测试水印",             //水印的内容（将被动态替换）
    watermark_x:20,                     //水印起始位置x轴坐标
    watermark_y:20,                     //水印起始位置Y轴坐标
    watermark_rows:0,                   //水印行数
    watermark_cols:0,                   //水印列数
    watermark_x_space:50,              //水印x轴间隔
    watermark_y_space:50,               //水印y轴间隔
    watermark_font:'微软雅黑',           //水印字体
    watermark_color:'black',            //水印字体颜色
    watermark_fontsize:'18px',          //水印字体大小
    watermark_alpha:0.15,               //水印透明度，要求设置在大于等于0.005
    watermark_width:150,                //水印宽度（增加以适应更长的文本）
    watermark_height:100,               //水印长度
    watermark_angle:15,                 //水印倾斜度数
    watermark_parent_width:0,      //水印的总体宽度（默认值：body的scrollWidth和clientWidth的较大值）
    watermark_parent_height:0,     //水印的总体高度（默认值：body的scrollHeight和clientHeight的较大值）
    watermark_parent_node:null,     //水印插件挂载的父元素element,不输入则默认挂在body上
    monitor:true,                   //monitor 是否监控， true: 不可删除水印; false: 可删水印。
    dynamic_update: true,           //是否启用动态更新
    api_url: '/polarion/watermark/api/currentUser', //API地址
    fallback_text: '默认水印',       //API调用失败时的降级文本
    time_format: 'YYYY-MM-DD HH:mm:ss' //时间格式
  };

  const MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;

  // 时间格式化函数
  function formatDateTime(date, format) {
    var year = date.getFullYear();
    var month = String(date.getMonth() + 1).padStart(2, '0');
    var day = String(date.getDate()).padStart(2, '0');
    var hours = String(date.getHours()).padStart(2, '0');
    var minutes = String(date.getMinutes()).padStart(2, '0');
    var seconds = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  // 构建水印文本
  function buildWatermarkText(userData) {
    if (!userData || !userData.success || !userData.pluginActive) {
      return null; // 插件无效或调用失败时返回null，不显示水印
    }

    var currentTime = new Date();
    var timeStr = formatDateTime(currentTime, defaultSettings.time_format);
    var username = userData.currentUser || '未知用户';

    // 支持换行显示：用户名换行时间
    return username + '\n' + timeStr;
  }

  // AJAX 调用函数 - 只调用一次
  function callCurrentUserAPI(callback) {
    if (apiCallInProgress) {
      return;
    }

    // 如果已经调用过API且有数据，直接使用缓存
    if (apiCalled && currentUserData) {
      callback(currentUserData);
      return;
    }

    apiCallInProgress = true;
    var xhr = new XMLHttpRequest();

    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4) {
        apiCallInProgress = false;
        apiCalled = true; // 标记API已调用

        try {
          if (xhr.status === 200) {
            var response = JSON.parse(xhr.responseText);
            currentUserData = response;
            callback(response);
          } else {
            console.warn('水印API调用失败，状态码:', xhr.status);
            currentUserData = { success: false, pluginActive: false, error: 'API调用失败' };
            callback(currentUserData);
          }
        } catch (e) {
          console.error('水印API响应解析失败:', e);
          currentUserData = { success: false, pluginActive: false, error: '响应解析失败' };
          callback(currentUserData);
        }
      }
    };

    xhr.onerror = function() {
      apiCallInProgress = false;
      apiCalled = true;
      console.error('水印API网络请求失败');
      currentUserData = { success: false, pluginActive: false, error: '网络请求失败' };
      callback(currentUserData);
    };

    xhr.timeout = 10000; // 10秒超时
    xhr.ontimeout = function() {
      apiCallInProgress = false;
      apiCalled = true;
      console.warn('水印API请求超时');
      currentUserData = { success: false, pluginActive: false, error: '请求超时' };
      callback(currentUserData);
    };

    try {
      xhr.open('GET', defaultSettings.api_url, true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('Cache-Control', 'no-cache');
      xhr.send();
    } catch (e) {
      apiCallInProgress = false;
      apiCalled = true;
      console.error('水印API请求发送失败:', e);
      currentUserData = { success: false, pluginActive: false, error: '请求发送失败' };
      callback(currentUserData);
    }
  }

  //监听dom是否被移除或者改变属性的回调函数
  var domChangeCallback = function (records){
    if(forceRemove) {
      forceRemove = false;
      return;
    }
    if ((globalSetting && records.length === 1) || records.length === 1 && records[0].removedNodes.length >= 1) {
      if (defaultSettings.dynamic_update && currentUserData) {
        loadMark(globalSetting, currentUserData);
      } else {
        loadMark(globalSetting);
      }
    }
  };

  var hasObserver = MutationObserver !== undefined;
  var watermarkDom = hasObserver ? new MutationObserver(domChangeCallback) : null;
  var option = {
    'childList': true,
    'attributes': true,
    'subtree': true,
  };

  /*加载水印*/
  var loadMark = function(settings, userData) {
    /*采用配置项替换默认值，作用类似jquery.extend*/
    if(arguments.length>=1&&typeof arguments[0] ==="object" ){
      var src=arguments[0]||{};
      for(key in src)
      {
        if(src[key]&&defaultSettings[key]&&src[key]===defaultSettings[key])continue;
        /*veronic: resolution of watermark_angle=0 not in force*/
        else if(src[key] || src[key] === 0) defaultSettings[key]=src[key];
      }
    }

    // 如果启用动态更新，构建动态水印文本
    if (defaultSettings.dynamic_update) {
      if (userData) {
        var watermarkText = buildWatermarkText(userData);
        if (watermarkText === null) {
          console.log('插件未激活或API调用失败，不显示水印');
          return;
        }
        defaultSettings.watermark_txt = watermarkText;
      } else {
        // 如果没有用户数据，不显示水印
        console.log('无用户数据，不显示水印');
        return;
      }
    }

    /*如果元素存在则移除*/
    var watermark_element = document.getElementById(defaultSettings.watermark_id);
    watermark_element && watermark_element.parentNode && watermark_element.parentNode.removeChild(watermark_element);

    /*如果设置水印挂载的父元素的id*/
    var watermark_parent_element = document.getElementById(defaultSettings.watermark_parent_node);
    var watermark_hook_element = watermark_parent_element ? watermark_parent_element : document.body;

    /*获取页面宽度*/
    // var page_width = Math.max(watermark_hook_element.scrollWidth,watermark_hook_element.clientWidth) - defaultSettings.watermark_width/2;
    var page_width = Math.max(watermark_hook_element.scrollWidth,watermark_hook_element.clientWidth);
    /*获取页面最大长度*/
    // var page_height = Math.max(watermark_hook_element.scrollHeight,watermark_hook_element.clientHeight,document.documentElement.clientHeight)-defaultSettings.watermark_height/2;
    var page_height = Math.max(watermark_hook_element.scrollHeight,watermark_hook_element.clientHeight);

    var setting = arguments[0]||{};
    var parentEle = watermark_hook_element;

    var page_offsetTop = 0;
    var page_offsetLeft = 0;
    if(setting.watermark_parent_width || setting.watermark_parent_height){
      /*指定父元素同时指定了宽或高*/
      if(parentEle){
        page_offsetTop = parentEle.offsetTop || 0;
        page_offsetLeft = parentEle.offsetLeft || 0;
        defaultSettings.watermark_x = defaultSettings.watermark_x + page_offsetLeft;
        defaultSettings.watermark_y = defaultSettings.watermark_y + page_offsetTop;
      }
    }else{
      if(parentEle){
        page_offsetTop = parentEle.offsetTop || 0;
        page_offsetLeft = parentEle.offsetLeft || 0;
      }
    }

    /*创建水印外壳div*/
    var otdiv = document.getElementById(defaultSettings.watermark_id);
    var shadowRoot = null;

    if(!otdiv){
      otdiv =document.createElement('div');
      /*创建shadow dom*/
      otdiv.id = defaultSettings.watermark_id;
      otdiv.setAttribute('style','pointer-events: none !important; display: block !important');
      /*判断浏览器是否支持attachShadow方法*/
      if(typeof otdiv.attachShadow === 'function'){
        /* createShadowRoot Deprecated. Not for use in new websites. Use attachShadow*/
        shadowRoot = otdiv.attachShadow({mode: 'open'});
      }else{
        shadowRoot = otdiv;
      }
      /*将shadow dom随机插入body内的任意位置*/
      var nodeList = watermark_hook_element.children;
      var index = Math.floor(Math.random()*(nodeList.length-1 ));
      if(nodeList[index]){
        watermark_hook_element.insertBefore(otdiv, nodeList[index]);
      }else{
        watermark_hook_element.appendChild(otdiv);
      }
    }else if (otdiv.shadowRoot){
      shadowRoot = otdiv.shadowRoot;
    }
    /*三种情况下会重新计算水印列数和x方向水印间隔：1、水印列数设置为0，2、水印宽度大于页面宽度，3、水印宽度小于于页面宽度*/
    defaultSettings.watermark_cols = parseInt((page_width - defaultSettings.watermark_x) / (defaultSettings.watermark_width + defaultSettings.watermark_x_space));
    var temp_watermark_x_space = parseInt((page_width - defaultSettings.watermark_x - defaultSettings.watermark_width * defaultSettings.watermark_cols) / (defaultSettings.watermark_cols));
    defaultSettings.watermark_x_space = temp_watermark_x_space? defaultSettings.watermark_x_space : temp_watermark_x_space;
    var allWatermarkWidth;

    /*三种情况下会重新计算水印行数和y方向水印间隔：1、水印行数设置为0，2、水印长度大于页面长度，3、水印长度小于于页面长度*/
    defaultSettings.watermark_rows = parseInt((page_height - defaultSettings.watermark_y) / (defaultSettings.watermark_height + defaultSettings.watermark_y_space));
    var temp_watermark_y_space = parseInt((page_height - defaultSettings.watermark_y - defaultSettings.watermark_height * defaultSettings.watermark_rows) / (defaultSettings.watermark_rows));
    defaultSettings.watermark_y_space = temp_watermark_y_space? defaultSettings.watermark_y_space : temp_watermark_y_space;
    var allWatermarkHeight;

    if(watermark_parent_element){
      allWatermarkWidth = defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1);
      allWatermarkHeight = defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1);
    }else{
      allWatermarkWidth = page_offsetLeft + defaultSettings.watermark_x + defaultSettings.watermark_width * defaultSettings.watermark_cols + defaultSettings.watermark_x_space * (defaultSettings.watermark_cols - 1);
      allWatermarkHeight = page_offsetTop + defaultSettings.watermark_y + defaultSettings.watermark_height * defaultSettings.watermark_rows + defaultSettings.watermark_y_space * (defaultSettings.watermark_rows - 1);
    }

    var x;
    var y;
    for (var i = 0; i < defaultSettings.watermark_rows; i++) {
      if(watermark_parent_element){
        y = page_offsetTop + defaultSettings.watermark_y + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
      }else{
        y = defaultSettings.watermark_y + (page_height - allWatermarkHeight) / 2 + (defaultSettings.watermark_y_space + defaultSettings.watermark_height) * i;
      }
      for (var j = 0; j < defaultSettings.watermark_cols; j++) {
        if(watermark_parent_element){
          x = page_offsetLeft + defaultSettings.watermark_x + (page_width - allWatermarkWidth) / 2 + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
        }else {
          x = defaultSettings.watermark_x + (page_width - allWatermarkWidth) / 2 + (defaultSettings.watermark_width + defaultSettings.watermark_x_space) * j;
        }
        var mask_div = document.createElement('div');

        // 支持换行显示
        if (defaultSettings.watermark_txt.indexOf('\n') !== -1) {
          // 如果包含换行符，使用innerHTML并替换换行符为<br>
          mask_div.innerHTML = defaultSettings.watermark_txt.replace(/\n/g, '<br>');
        } else {
          // 如果不包含换行符，使用原来的方式
          var oText = document.createTextNode(defaultSettings.watermark_txt);
          mask_div.appendChild(oText);
        }
        /*设置水印相关属性start*/
        mask_div.id = defaultSettings.watermark_prefix + i + j;
        /*设置水印div倾斜显示*/
        mask_div.style.webkitTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
        mask_div.style.MozTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
        mask_div.style.msTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
        mask_div.style.OTransform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
        mask_div.style.transform = "rotate(-" + defaultSettings.watermark_angle + "deg)";
        mask_div.style.visibility = "";
        mask_div.style.position = "absolute";
        /*选不中*/
        mask_div.style.left = x + 'px';
        mask_div.style.top = y + 'px';
        mask_div.style.overflow = "hidden";
        mask_div.style.zIndex = "9999999";
        mask_div.style.opacity = defaultSettings.watermark_alpha;
        mask_div.style.fontSize = defaultSettings.watermark_fontsize;
        mask_div.style.fontFamily = defaultSettings.watermark_font;
        mask_div.style.color = defaultSettings.watermark_color;
        mask_div.style.textAlign = "center";
        mask_div.style.width = defaultSettings.watermark_width + 'px';
        mask_div.style.height = defaultSettings.watermark_height + 'px';
        mask_div.style.display = "block";
        mask_div.style['-ms-user-select'] = "none";
        /*设置水印相关属性end*/
        shadowRoot.appendChild(mask_div);
      }
    }

    // monitor 是否监控， true: 不可删除水印; false: 可删水印。
    const minotor = settings.monitor === undefined ? defaultSettings.monitor : settings.monitor;
    if (minotor && hasObserver) {
      watermarkDom.observe(watermark_hook_element, option);
      watermarkDom.observe(document.getElementById(defaultSettings.watermark_id).shadowRoot, option);
    }
  };

  /*移除水印*/
  var removeMark = function() {
    /*采用配置项替换默认值，作用类似jquery.extend*/
    if(arguments.length===1&&typeof arguments[0] ==="object" )
    {
      var src=arguments[0]||{};
      for(key in src)
      {
        if(src[key]&&defaultSettings[key]&&src[key]===defaultSettings[key])continue;
        /*veronic: resolution of watermark_angle=0 not in force*/
        else if(src[key] || src[key] === 0) defaultSettings[key]=src[key];
      }
    }

    /*移除水印*/
    var watermark_element = document.getElementById(defaultSettings.watermark_id);
    var _parentElement = watermark_element.parentNode;
    _parentElement.removeChild(watermark_element);
    // :ambulance: remove()
    // minotor 这个配置有些冗余
    // 如果用 MutationObserver 来监听dom变化防止删除水印
    // remove() 方法里用 MutationObserver 的 disconnect() 解除监听即可
    watermarkDom.disconnect();
  };

  var globalSetting;

  // 动态更新水印的函数 - 只更新时间，不重新调用API
  function updateWatermarkWithCurrentTime() {
    if (!globalSetting || !defaultSettings.dynamic_update || !currentUserData) {
      return;
    }

    // 只有当用户数据存在且插件有效时才更新
    if (currentUserData.success && currentUserData.pluginActive) {
      loadMark(globalSetting, currentUserData);
    }
  }

  // 启动定时更新
  function startTimedUpdate() {
    if (updateTimer) {
      clearInterval(updateTimer);
    }

    if (defaultSettings.dynamic_update) {
      updateTimer = setInterval(function() {
        updateWatermarkWithCurrentTime();
      }, TIME_UPDATE_INTERVAL);
    }
  }

  // 停止定时更新
  function stopTimedUpdate() {
    if (updateTimer) {
      clearInterval(updateTimer);
      updateTimer = null;
    }
  }

  /*初始化水印 - 一步到位*/
  watermark.init = function(settings) {
    // 直接调用load函数，实现一步到位的初始化
    watermark.load(settings);
  };

  /*手动加载水印 - 一步到位的初始化*/
  watermark.load = function(settings){
    globalSetting = settings;

    // 默认启用动态更新，除非明确设置为false
    var enableDynamic = settings && settings.dynamic_update !== false;

    if (enableDynamic) {
      // 启用动态更新：调用API获取用户信息并启动定时更新
      callCurrentUserAPI(function(userData) {
        loadMark(settings, userData);
        startTimedUpdate();

        // 添加窗口事件监听
        window.addEventListener('resize', function () {
          if (defaultSettings.dynamic_update && currentUserData) {
            loadMark(settings, currentUserData);
          } else {
            loadMark(settings);
          }
        });
      });
    } else {
      // 静态水印：直接加载
      loadMark(settings);

      // 添加窗口事件监听
      window.addEventListener('resize', function () {
        loadMark(settings);
      });
    }
  };

  /*手动移除水印*/
  watermark.remove = function(){
    forceRemove = true;
    stopTimedUpdate(); // 停止定时更新
    removeMark();
  };

  /*刷新用户数据 - 重新调用API*/
  watermark.refreshUserData = function() {
    if (!defaultSettings.dynamic_update) {
      return;
    }

    // 重置API调用标记，允许重新调用
    apiCalled = false;
    currentUserData = null;

    callCurrentUserAPI(function(userData) {
      if (globalSetting) {
        loadMark(globalSetting, userData);
      }
    });
  };

  /*获取当前用户数据*/
  watermark.getCurrentUserData = function() {
    return currentUserData;
  };

  /*设置API地址*/
  watermark.setApiUrl = function(url) {
    defaultSettings.api_url = url;
  };


  //监听dom是否被移除或者改变属性的回调函数
  var callback = function (records){
    if ((globalSetting && records.length === 1) || records.length === 1 && records[0].removedNodes.length >= 1) {
      if (defaultSettings.dynamic_update && currentUserData) {
        loadMark(globalSetting, currentUserData);
      } else {
        loadMark(globalSetting);
      }
      return;
    }

    // 监听父节点的尺寸是否发生了变化, 如果发生改变, 则进行重新绘制
    var watermark_parent_element = document.getElementById(defaultSettings.watermark_parent_node);
    if (watermark_parent_element) {
      var newWidth = getComputedStyle(watermark_parent_element).getPropertyValue('width');
      var newHeight = getComputedStyle(watermark_parent_element).getPropertyValue('height');
      if (newWidth !== recordOldValue.width || newHeight !== recordOldValue.height) {
        recordOldValue.width = newWidth;
        recordOldValue.height = newHeight;
        if (defaultSettings.dynamic_update && currentUserData) {
          loadMark(globalSetting, currentUserData);
        } else {
          loadMark(globalSetting);
        }
      }
    }
  };
  //const MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;
  var watermarkDom = new MutationObserver(callback);
  var option = {
    'childList': true,
    'attributes': true,
    'subtree': true,
    'attributeFilter': ['style'],
    'attributeOldValue': true
  };
  var recordOldValue = {
    width: 0,
    height: 0
  }

  /*快速启动 - 使用默认配置的动态水印*/
  watermark.start = function(customSettings) {
    var settings = customSettings || {};
    // 确保启用动态更新
    settings.dynamic_update = true;
    watermark.load(settings);
  };

  /*快速启动静态水印*/
  watermark.startStatic = function(text, customSettings) {
    var settings = customSettings || {};
    settings.dynamic_update = false;
    settings.watermark_txt = text || '静态水印';
    watermark.load(settings);
  };

  return watermark;
}));
